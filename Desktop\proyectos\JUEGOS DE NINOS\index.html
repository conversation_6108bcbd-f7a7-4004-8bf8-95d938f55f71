<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fruity Numbers - Juego Educativo para Niños</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Pantalla de Inicio -->
    <div id="startScreen" class="screen active">
        <div class="title-container">
            <h1 class="game-title">🍎 Fruity Numbers 🍊</h1>
            <p class="subtitle">¡Aprende números mientras juegas!</p>
        </div>
        <div class="character-preview">
            <div class="character jumping"></div>
        </div>
        <button id="startButton" class="btn-primary">
            🎮 ¡JUGAR!
        </button>
        <div class="instructions">
            <p>🎯 Recoge las frutas con números</p>
            <p>⌨️ Usa ESPACIO para saltar</p>
            <p>📱 Toca la pantalla en móvil</p>
        </div>
    </div>

    <!-- Pantalla de Juego -->
    <div id="gameScreen" class="screen">
        <div class="game-ui">
            <div class="score-container">
                <span class="score-label">Puntos:</span>
                <span id="score">0</span>
            </div>
            <div class="level-container">
                <span class="level-label">Nivel:</span>
                <span id="level">1</span>
            </div>
            <div class="target-container">
                <span class="target-label">Busca el número:</span>
                <span id="targetNumber">1</span>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="400"></canvas>
        
        <div class="mobile-controls">
            <button id="jumpButton" class="jump-btn">
                ⬆️ SALTAR
            </button>
        </div>
    </div>

    <!-- Pantalla de Game Over -->
    <div id="gameOverScreen" class="screen">
        <div class="game-over-container">
            <h2 class="game-over-title">🎉 ¡Bien Jugado! 🎉</h2>
            <div class="final-stats">
                <p>Puntuación Final: <span id="finalScore">0</span></p>
                <p>Nivel Alcanzado: <span id="finalLevel">1</span></p>
                <p>Números Aprendidos: <span id="numbersLearned">0</span></p>
            </div>
            <div class="celebration">
                <div class="star">⭐</div>
                <div class="star">⭐</div>
                <div class="star">⭐</div>
            </div>
            <button id="playAgainButton" class="btn-primary">
                🔄 ¡JUGAR DE NUEVO!
            </button>
            <button id="backToMenuButton" class="btn-secondary">
                🏠 MENÚ PRINCIPAL
            </button>
        </div>
    </div>

    <!-- Efectos de Partículas -->
    <div id="particles"></div>

    <!-- Scripts -->
    <script src="game.js"></script>
</body>
</html>
